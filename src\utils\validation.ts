import Joi from 'joi';
import { Request, Response, NextFunction } from 'express';
import { ApiResponse } from '../types/index.js';

export const validateSchema = (schema: Joi.ObjectSchema) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const { error, value } = schema.validate(req.body, {
      abortEarly: false,
      stripUnknown: true,
    });

    if (error) {
      const errors = error.details.map(detail => detail.message);
      const response: ApiResponse = {
        success: false,
        message: 'Validation failed',
        errors,
      };
      return res.status(400).json(response);
    }

    req.body = value;
    next();
  };
};

export const validateQuery = (schema: Joi.ObjectSchema) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const { error, value } = schema.validate(req.query, {
      abortEarly: false,
      stripUnknown: true,
    });

    if (error) {
      const errors = error.details.map(detail => detail.message);
      const response: ApiResponse = {
        success: false,
        message: 'Query validation failed',
        errors,
      };
      return res.status(400).json(response);
    }

    req.query = value;
    next();
  };
};

export const validateParams = (schema: Joi.ObjectSchema) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const { error, value } = schema.validate(req.params, {
      abortEarly: false,
      stripUnknown: true,
    });

    if (error) {
      const errors = error.details.map(detail => detail.message);
      const response: ApiResponse = {
        success: false,
        message: 'Parameter validation failed',
        errors,
      };
      return res.status(400).json(response);
    }

    req.params = value;
    next();
  };
};

// Common validation schemas
export const schemas = {
  user: {
    register: Joi.object({
      email: Joi.string().email().required(),
      password: Joi.string().min(8).required(),
      user_type: Joi.string().valid('worker', 'company').required(),
      first_name: Joi.string().min(2).max(50).required(),
      last_name: Joi.string().min(2).max(50).required(),
      company_name: Joi.when('user_type', {
        is: 'company',
        then: Joi.string().min(2).max(100).required(),
        otherwise: Joi.string().optional(),
      }),
      phone: Joi.string().pattern(/^\+?[\d\s\-\(\)]+$/).optional(),
    }),
    
    login: Joi.object({
      email: Joi.string().email().required(),
      password: Joi.string().required(),
    }),
    
    update: Joi.object({
      first_name: Joi.string().min(2).max(50).optional(),
      last_name: Joi.string().min(2).max(50).optional(),
      company_name: Joi.string().min(2).max(100).optional(),
      phone: Joi.string().pattern(/^\+?[\d\s\-\(\)]+$/).optional(),
      avatar_url: Joi.string().uri().optional(),
    }),
  },
  
  pagination: Joi.object({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(20),
    sort_by: Joi.string().optional(),
    sort_order: Joi.string().valid('asc', 'desc').default('desc'),
  }),
  
  chat: {
    createRoom: Joi.object({
      name: Joi.string().min(1).max(100).optional(),
      type: Joi.string().valid('direct', 'group').required(),
      participant_ids: Joi.array().items(Joi.string().uuid()).min(1).required(),
    }),
    
    sendMessage: Joi.object({
      content: Joi.string().min(1).max(5000).required(),
      message_type: Joi.string().valid('text', 'image', 'file').default('text'),
      reply_to_id: Joi.string().uuid().optional(),
    }),
  },
  
  params: {
    uuid: Joi.object({
      id: Joi.string().uuid().required(),
    }),
  },
};
