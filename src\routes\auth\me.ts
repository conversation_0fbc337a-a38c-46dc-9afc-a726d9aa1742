import { Request, Response } from 'express';
import { UserService } from '../../services/user.service.ts';
import { ResponseHelper } from '../../utils/response.ts';
import { logger } from '../../utils/logger.ts';
import { asyncHandler } from '../router.ts';
import { AuthenticatedRequest } from '../../middleware/auth.middleware.ts';

export const GET = asyncHandler(async (req: Request, res: Response) => {
  try {
    const user = (req as AuthenticatedRequest).user;

    const userProfile = await UserService.getUserById(user.id);

    if (!userProfile) {
      return ResponseHelper.notFound(res, 'User not found');
    }

    // Get user permissions
    const permissions = await UserService.getUserPermissions(user.id);

    return ResponseHelper.success(res, {
      ...userProfile,
      permissions,
    }, 'User profile retrieved successfully');

  } catch (error) {
    logger.error('Error fetching user profile:', error);
    
    const message = error instanceof Error ? error.message : 'Failed to fetch user profile';
    return ResponseHelper.error(res, message);
  }
});
