import { Response } from 'express';
import { ApiResponse } from '../types/index.js';

export class ResponseHelper {
  static success<T>(res: Response, data?: T, message?: string, statusCode: number = 200): Response {
    const response: ApiResponse<T> = {
      success: true,
      data,
      message,
    };
    return res.status(statusCode).json(response);
  }

  static error(res: Response, message: string, errors?: string[], statusCode: number = 400): Response {
    const response: ApiResponse = {
      success: false,
      message,
      errors,
    };
    return res.status(statusCode).json(response);
  }

  static paginated<T>(
    res: Response,
    data: T[],
    page: number,
    limit: number,
    total: number,
    message?: string
  ): Response {
    const totalPages = Math.ceil(total / limit);
    
    const response: ApiResponse<T[]> = {
      success: true,
      data,
      message,
      pagination: {
        page,
        limit,
        total,
        total_pages: totalPages,
      },
    };
    
    return res.status(200).json(response);
  }

  static created<T>(res: Response, data?: T, message?: string): Response {
    return this.success(res, data, message, 201);
  }

  static noContent(res: Response): Response {
    return res.status(204).send();
  }

  static unauthorized(res: Response, message: string = 'Unauthorized'): Response {
    return this.error(res, message, undefined, 401);
  }

  static forbidden(res: Response, message: string = 'Forbidden'): Response {
    return this.error(res, message, undefined, 403);
  }

  static notFound(res: Response, message: string = 'Resource not found'): Response {
    return this.error(res, message, undefined, 404);
  }

  static conflict(res: Response, message: string = 'Resource already exists'): Response {
    return this.error(res, message, undefined, 409);
  }

  static validationError(res: Response, errors: string[]): Response {
    return this.error(res, 'Validation failed', errors, 400);
  }

  static serverError(res: Response, message: string = 'Internal server error'): Response {
    return this.error(res, message, undefined, 500);
  }
}

export default ResponseHelper;
