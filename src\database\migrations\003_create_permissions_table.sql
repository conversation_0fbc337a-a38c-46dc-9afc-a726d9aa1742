-- Create permissions table for role-based access control
CREATE TABLE IF NOT EXISTS permissions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    resource VARCHAR(100) NOT NULL,
    action VARCHAR(50) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create user_permissions junction table
CREATE TABLE IF NOT EXISTS user_permissions (
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    permission_id UUID NOT NULL REFERENCES permissions(id) ON DELETE CASCADE,
    granted_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    granted_by UUID REFERENCES users(id),
    PRIMARY KEY (user_id, permission_id)
);

-- <PERSON>reate indexes
CREATE INDEX IF NOT EXISTS idx_permissions_resource ON permissions(resource);
CREATE INDEX IF NOT EXISTS idx_permissions_action ON permissions(action);
CREATE INDEX IF NOT EXISTS idx_user_permissions_user_id ON user_permissions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_permissions_permission_id ON user_permissions(permission_id);

-- Insert default permissions
INSERT INTO permissions (name, description, resource, action) VALUES
('user.read', 'Read user information', 'user', 'read'),
('user.update', 'Update user information', 'user', 'update'),
('user.delete', 'Delete user account', 'user', 'delete'),
('chat.create', 'Create chat rooms', 'chat', 'create'),
('chat.read', 'Read chat messages', 'chat', 'read'),
('chat.write', 'Send chat messages', 'chat', 'write'),
('chat.moderate', 'Moderate chat rooms', 'chat', 'moderate'),
('admin.users', 'Manage all users', 'admin', 'users'),
('admin.permissions', 'Manage permissions', 'admin', 'permissions')
ON CONFLICT (name) DO NOTHING;
