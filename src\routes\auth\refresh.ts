import { Request, Response } from 'express';
import <PERSON><PERSON> from 'joi';
import { AuthService } from '../../services/auth.service.ts';
import { ResponseHelper } from '../../utils/response.ts';
import { validateSchema } from '../../utils/validation.ts';
import { logger } from '../../utils/logger.ts';
import { asyncHandler } from '../router.ts';

const refreshTokenSchema = Joi.object({
  refresh_token: Joi.string().required(),
});

export const middleware = [validateSchema(refreshTokenSchema)];
export const public = true; // This route doesn't require authentication

export const POST = asyncHandler(async (req: Request, res: Response) => {
  try {
    const { refresh_token } = req.body;

    const tokens = await AuthService.refreshToken(refresh_token);

    logger.info('Token refresh successful');

    return ResponseHelper.success(res, { tokens }, 'Token refreshed successfully');

  } catch (error) {
    logger.error('Token refresh failed:', error);
    
    const message = error instanceof Error ? error.message : 'Token refresh failed';
    return ResponseHelper.error(res, message, undefined, 401);
  }
});
