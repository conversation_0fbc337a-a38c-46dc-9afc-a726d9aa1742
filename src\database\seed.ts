import { db } from '../config/database.ts';
import { AuthService } from '../services/auth.service.ts';
import { logger } from '../utils/logger.ts';

class DatabaseSeeder {
  async run(): Promise<void> {
    try {
      logger.info('Starting database seeding...');
      
      // Connect to database
      await db.connect();
      
      // Create sample users
      await this.createSampleUsers();
      
      logger.info('Database seeding completed successfully');
      
    } catch (error) {
      logger.error('Database seeding failed:', error);
      throw error;
    }
  }

  private async createSampleUsers(): Promise<void> {
    try {
      // Check if users already exist
      const existingUsers = await db.query('SELECT COUNT(*) as count FROM users');
      const userCount = parseInt(existingUsers.rows[0].count);
      
      if (userCount > 0) {
        logger.info('Users already exist, skipping user creation');
        return;
      }

      // Create sample company user
      const companyUser = await AuthService.register({
        email: '<EMAIL>',
        password: 'password123',
        user_type: 'company',
        first_name: 'Tech',
        last_name: 'Company',
        company_name: 'Tech Solutions Inc.',
        phone: '******-0123',
      });

      logger.info('Sample company user created', { user_id: companyUser.user.id });

      // Create sample worker users
      const workerUsers = [
        {
          email: '<EMAIL>',
          password: 'password123',
          user_type: 'worker' as const,
          first_name: 'John',
          last_name: 'Doe',
          phone: '******-0124',
        },
        {
          email: '<EMAIL>',
          password: 'password123',
          user_type: 'worker' as const,
          first_name: 'Jane',
          last_name: 'Smith',
          phone: '******-0125',
        },
        {
          email: '<EMAIL>',
          password: 'password123',
          user_type: 'worker' as const,
          first_name: 'Mike',
          last_name: 'Johnson',
          phone: '******-0126',
        },
      ];

      for (const userData of workerUsers) {
        const worker = await AuthService.register(userData);
        logger.info('Sample worker user created', { 
          user_id: worker.user.id, 
          email: worker.user.email 
        });
      }

      logger.info('Sample users created successfully');
      
    } catch (error) {
      logger.error('Error creating sample users:', error);
      throw error;
    }
  }
}

const seeder = new DatabaseSeeder();

seeder.run()
  .then(() => {
    logger.info('Seeding completed');
    process.exit(0);
  })
  .catch((error) => {
    logger.error('Seeding failed:', error);
    process.exit(1);
  });

export default DatabaseSeeder;
