import { Server as SocketIOServer, Socket } from 'socket.io';
import { Server as HTTPServer } from 'http';
import jwt from 'jsonwebtoken';
import { config } from '../config/index.ts';
import { logger } from '../utils/logger.ts';
import { ChatService } from './chat.service.ts';
import { UserService } from './user.service.ts';
import { JWTPayload, SocketUser } from '../types/index.ts';

interface AuthenticatedSocket extends Socket {
  user?: {
    id: string;
    email: string;
    user_type: 'worker' | 'company';
  };
}

export class SocketService {
  private io: SocketIOServer;
  private connectedUsers: Map<string, SocketUser> = new Map();
  private userSockets: Map<string, string[]> = new Map(); // userId -> socketIds[]

  constructor(server: HTTPServer) {
    this.io = new SocketIOServer(server, {
      cors: {
        origin: [
          config.client_url,
          'http://localhost:3000',
          'http://localhost:3001',
          'http://127.0.0.1:3000',
        ],
        methods: ['GET', 'POST'],
        credentials: true,
      },
      transports: ['websocket', 'polling'],
    });

    this.setupMiddleware();
    this.setupEventHandlers();
  }

  private setupMiddleware(): void {
    // Authentication middleware
    this.io.use(async (socket: AuthenticatedSocket, next) => {
      try {
        const token = socket.handshake.auth.token || socket.handshake.headers.authorization?.replace('Bearer ', '');
        
        if (!token) {
          return next(new Error('Authentication token required'));
        }

        const decoded: JWTPayload = jwt.verify(token, config.jwt_secret) as JWTPayload;
        const user = await UserService.getUserById(decoded.user_id);

        if (!user || !user.is_active) {
          return next(new Error('User not found or inactive'));
        }

        socket.user = {
          id: user.id,
          email: user.email,
          user_type: user.user_type,
        };

        next();
      } catch (error) {
        logger.error('Socket authentication failed:', error);
        next(new Error('Authentication failed'));
      }
    });
  }

  private setupEventHandlers(): void {
    this.io.on('connection', (socket: AuthenticatedSocket) => {
      if (!socket.user) return;

      this.handleUserConnection(socket);
      this.handleChatEvents(socket);
      this.handleDisconnection(socket);
    });
  }

  private handleUserConnection(socket: AuthenticatedSocket): void {
    const user = socket.user!;
    
    // Store connected user
    this.connectedUsers.set(socket.id, {
      user_id: user.id,
      socket_id: socket.id,
      user_type: user.user_type,
      connected_at: new Date(),
    });

    // Track user sockets
    const userSockets = this.userSockets.get(user.id) || [];
    userSockets.push(socket.id);
    this.userSockets.set(user.id, userSockets);

    logger.info('User connected to socket', { 
      user_id: user.id, 
      socket_id: socket.id,
      total_connections: userSockets.length 
    });

    // Join user to their personal room for direct messaging
    socket.join(`user:${user.id}`);

    // Emit user online status to relevant users
    this.broadcastUserStatus(user.id, 'online');
  }

  private handleChatEvents(socket: AuthenticatedSocket): void {
    const user = socket.user!;

    // Join chat room
    socket.on('join_room', async (data: { room_id: string }) => {
      try {
        const { room_id } = data;
        
        // Verify user can access this room
        const room = await ChatService.getRoomById(room_id, user.id);
        if (!room) {
          socket.emit('error', { message: 'Room not found or access denied' });
          return;
        }

        socket.join(`room:${room_id}`);
        socket.emit('joined_room', { room_id, room });
        
        logger.debug('User joined room', { user_id: user.id, room_id });
      } catch (error) {
        logger.error('Error joining room:', error);
        socket.emit('error', { message: 'Failed to join room' });
      }
    });

    // Leave chat room
    socket.on('leave_room', (data: { room_id: string }) => {
      const { room_id } = data;
      socket.leave(`room:${room_id}`);
      socket.emit('left_room', { room_id });
      
      logger.debug('User left room', { user_id: user.id, room_id });
    });

    // Send message
    socket.on('send_message', async (data: {
      room_id: string;
      content: string;
      message_type?: 'text' | 'image' | 'file';
      reply_to_id?: string;
    }) => {
      try {
        const { room_id, content, message_type = 'text', reply_to_id } = data;

        const message = await ChatService.sendMessage(
          room_id,
          user.id,
          content,
          message_type,
          reply_to_id
        );

        // Get sender info for the message
        const senderInfo = await UserService.getUserById(user.id);
        const messageWithSender = {
          ...message,
          sender: {
            id: senderInfo!.id,
            first_name: senderInfo!.first_name,
            last_name: senderInfo!.last_name,
            avatar_url: senderInfo!.avatar_url,
            user_type: senderInfo!.user_type,
          },
        };

        // Broadcast message to room participants
        this.io.to(`room:${room_id}`).emit('new_message', messageWithSender);

        logger.debug('Message sent', { 
          message_id: message.id, 
          room_id, 
          sender_id: user.id 
        });
      } catch (error) {
        logger.error('Error sending message:', error);
        socket.emit('error', { message: 'Failed to send message' });
      }
    });

    // Mark messages as read
    socket.on('mark_read', async (data: { room_id: string }) => {
      try {
        const { room_id } = data;
        await ChatService.updateLastRead(room_id, user.id);
        
        // Notify other participants that user has read messages
        socket.to(`room:${room_id}`).emit('user_read', {
          room_id,
          user_id: user.id,
          read_at: new Date(),
        });
      } catch (error) {
        logger.error('Error marking messages as read:', error);
      }
    });

    // Typing indicators
    socket.on('typing_start', (data: { room_id: string }) => {
      socket.to(`room:${data.room_id}`).emit('user_typing', {
        room_id: data.room_id,
        user_id: user.id,
        typing: true,
      });
    });

    socket.on('typing_stop', (data: { room_id: string }) => {
      socket.to(`room:${data.room_id}`).emit('user_typing', {
        room_id: data.room_id,
        user_id: user.id,
        typing: false,
      });
    });

    // Direct message initiation
    socket.on('start_direct_chat', async (data: { target_user_id: string }) => {
      try {
        const { target_user_id } = data;

        // Check if direct room already exists
        let room = await ChatService.getDirectRoom(user.id, target_user_id);

        if (!room) {
          // Create new direct room
          room = await ChatService.createRoom(
            user.id,
            'direct',
            [target_user_id]
          );
        }

        socket.emit('direct_chat_ready', { room });
        
        // Notify target user if they're online
        this.io.to(`user:${target_user_id}`).emit('new_direct_chat', { room });

      } catch (error) {
        logger.error('Error starting direct chat:', error);
        socket.emit('error', { message: 'Failed to start direct chat' });
      }
    });
  }

  private handleDisconnection(socket: AuthenticatedSocket): void {
    socket.on('disconnect', () => {
      if (!socket.user) return;

      const user = socket.user;
      
      // Remove from connected users
      this.connectedUsers.delete(socket.id);

      // Update user sockets tracking
      const userSockets = this.userSockets.get(user.id) || [];
      const updatedSockets = userSockets.filter(id => id !== socket.id);
      
      if (updatedSockets.length === 0) {
        this.userSockets.delete(user.id);
        // User is completely offline
        this.broadcastUserStatus(user.id, 'offline');
      } else {
        this.userSockets.set(user.id, updatedSockets);
      }

      logger.info('User disconnected from socket', { 
        user_id: user.id, 
        socket_id: socket.id,
        remaining_connections: updatedSockets.length 
      });
    });
  }

  private broadcastUserStatus(userId: string, status: 'online' | 'offline'): void {
    // This could be enhanced to only notify relevant users (friends, chat participants, etc.)
    this.io.emit('user_status_change', {
      user_id: userId,
      status,
      timestamp: new Date(),
    });
  }

  // Public methods for external use
  public sendToUser(userId: string, event: string, data: any): void {
    this.io.to(`user:${userId}`).emit(event, data);
  }

  public sendToRoom(roomId: string, event: string, data: any): void {
    this.io.to(`room:${roomId}`).emit(event, data);
  }

  public isUserOnline(userId: string): boolean {
    return this.userSockets.has(userId);
  }

  public getConnectedUsers(): SocketUser[] {
    return Array.from(this.connectedUsers.values());
  }

  public getUserConnectionCount(userId: string): number {
    return this.userSockets.get(userId)?.length || 0;
  }
}
