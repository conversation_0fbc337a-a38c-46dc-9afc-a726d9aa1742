import dotenv from 'dotenv';
import { ServerConfig } from '../types/index.ts';

// Load environment variables
dotenv.config();

export const config: ServerConfig = {
  port: parseInt(process.env.PORT || '3000'),
  node_env: process.env.NODE_ENV || 'development',
  client_url: process.env.CLIENT_URL || 'http://localhost:3000',
  jwt_secret: process.env.JWT_SECRET || 'your-super-secret-jwt-key',
  jwt_expires_in: process.env.JWT_EXPIRES_IN || '7d',
  jwt_refresh_expires_in: process.env.JWT_REFRESH_EXPIRES_IN || '30d',
  rate_limit_window_ms: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000'),
  rate_limit_max_requests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100'),
};

// Validate required environment variables
const requiredEnvVars = [
  'POSTGRES_HOST',
  'POSTGRES_PORT',
  'POSTGRES_DB',
  'POSTGRES_USER',
  'POSTGRES_PASSWORD',
  'JWT_SECRET',
];

const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar]);

if (missingEnvVars.length > 0) {
  throw new Error(`Missing required environment variables: ${missingEnvVars.join(', ')}`);
}

export default config;
