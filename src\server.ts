import express from 'express';
import { createServer } from 'http';
import path from 'path';
import { config } from './config/index.ts';
import { db } from './config/database.ts';
import { logger } from './utils/logger.ts';
import { AutoRouter } from './routes/router.ts';
import { SocketService } from './services/socket.service.ts';
import {
  securityMiddleware,
  corsMiddleware,
  rateLimitMiddleware,
  compressionMiddleware,
  loggingMiddleware,
  errorHandler,
  notFoundHandler,
  timeoutMiddleware,
} from './middleware/common.middleware.ts';
import {
  metricsMiddleware,
  errorTrackingMiddleware,
  startMetricsLogging,
} from './utils/monitoring.ts';

class Server {
  private app: express.Application;
  private server: any;
  private socketService: SocketService | null = null;
  private autoRouter: AutoRouter | null = null;

  constructor() {
    this.app = express();
    this.server = createServer(this.app);
  }

  async initialize(): Promise<void> {
    try {
      // Connect to database
      await this.connectDatabase();

      // Setup middleware
      this.setupMiddleware();

      // Setup routes
      await this.setupRoutes();

      // Setup Socket.IO
      this.setupSocket();

      // Setup error handling
      this.setupErrorHandling();

      // Start metrics logging
      this.startMonitoring();

      logger.info('Server initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize server:', error);
      throw error;
    }
  }

  private async connectDatabase(): Promise<void> {
    try {
      await db.connect();
      logger.info('Database connected successfully');
    } catch (error) {
      logger.error('Database connection failed:', error);
      throw error;
    }
  }

  private setupMiddleware(): void {
    // Security middleware
    this.app.use(securityMiddleware);

    // CORS
    this.app.use(corsMiddleware);

    // Request timeout
    this.app.use(timeoutMiddleware(30000)); // 30 seconds

    // Rate limiting
    this.app.use(rateLimitMiddleware);

    // Compression
    this.app.use(compressionMiddleware);

    // Request logging
    this.app.use(loggingMiddleware);

    // Metrics tracking
    this.app.use(metricsMiddleware);

    // Body parsing
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // Trust proxy (for accurate IP addresses behind reverse proxy)
    this.app.set('trust proxy', 1);

    logger.info('Middleware setup completed');
  }

  private async setupRoutes(): Promise<void> {
    try {
      // Initialize auto-router
      const routesPath = path.join(process.cwd(), 'src', 'routes');
      this.autoRouter = new AutoRouter(routesPath);
      const router = await this.autoRouter.initialize();

      // Mount API routes
      this.app.use('/api', router);

      // Root endpoint
      this.app.get('/', (req, res) => {
        res.json({
          success: true,
          message: 'CheffUp API is running',
          version: process.env.npm_package_version || '1.0.0',
          environment: config.node_env,
          timestamp: new Date().toISOString(),
        });
      });

      logger.info('Routes setup completed', {
        publicRoutes: this.autoRouter.getPublicRoutes(),
      });
    } catch (error) {
      logger.error('Routes setup failed:', error);
      throw error;
    }
  }

  private setupSocket(): void {
    try {
      this.socketService = new SocketService(this.server);
      logger.info('Socket.IO setup completed');
    } catch (error) {
      logger.error('Socket.IO setup failed:', error);
      throw error;
    }
  }

  private setupErrorHandling(): void {
    // Error tracking middleware
    this.app.use(errorTrackingMiddleware);

    // 404 handler
    this.app.use(notFoundHandler);

    // Global error handler
    this.app.use(errorHandler);

    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      logger.error('Uncaught Exception:', error);
      this.gracefulShutdown('UNCAUGHT_EXCEPTION');
    });

    // Handle unhandled promise rejections
    process.on('unhandledRejection', (reason, promise) => {
      logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
      this.gracefulShutdown('UNHANDLED_REJECTION');
    });

    // Handle SIGTERM
    process.on('SIGTERM', () => {
      logger.info('SIGTERM received');
      this.gracefulShutdown('SIGTERM');
    });

    // Handle SIGINT
    process.on('SIGINT', () => {
      logger.info('SIGINT received');
      this.gracefulShutdown('SIGINT');
    });

    logger.info('Error handling setup completed');
  }

  private startMonitoring(): void {
    // Start periodic metrics logging (every 5 minutes)
    startMetricsLogging(300000);
    logger.info('Monitoring started');
  }

  async start(): Promise<void> {
    try {
      await this.initialize();

      this.server.listen(config.port, () => {
        logger.info(`Server started successfully`, {
          port: config.port,
          environment: config.node_env,
          nodeVersion: process.version,
          pid: process.pid,
        });

        // Log server URLs
        logger.info('Server URLs:', {
          api: `http://localhost:${config.port}/api`,
          health: `http://localhost:${config.port}/api/health`,
          metrics: `http://localhost:${config.port}/api/metrics`,
        });
      });
    } catch (error) {
      logger.error('Failed to start server:', error);
      process.exit(1);
    }
  }

  private async gracefulShutdown(signal: string): Promise<void> {
    logger.info(`Graceful shutdown initiated by ${signal}`);

    // Stop accepting new connections
    this.server.close(async () => {
      logger.info('HTTP server closed');

      try {
        // Close database connections
        await db.disconnect();
        logger.info('Database connections closed');

        // Exit process
        logger.info('Graceful shutdown completed');
        process.exit(0);
      } catch (error) {
        logger.error('Error during graceful shutdown:', error);
        process.exit(1);
      }
    });

    // Force shutdown after 30 seconds
    setTimeout(() => {
      logger.error('Forced shutdown after timeout');
      process.exit(1);
    }, 30000);
  }

  // Getter methods for testing or external access
  getApp(): express.Application {
    return this.app;
  }

  getServer(): any {
    return this.server;
  }

  getSocketService(): SocketService | null {
    return this.socketService;
  }
}
const server = new Server();
server.start().catch((error) => {
  logger.error('Failed to start server:', error);
  process.exit(1);
});

export default Server;
