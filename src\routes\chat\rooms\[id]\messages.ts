import { Request, Response } from 'express';
import { ChatService } from '../../../../services/chat.service.js';
import { ResponseHelper } from '../../../../utils/response.js';
import { validateParams, validateSchema, schemas } from '../../../../utils/validation.js';
import { paginationMiddleware } from '../../../../middleware/common.middleware.js';
import { requirePermission } from '../../../../middleware/auth.middleware.js';
import { logger } from '../../../../utils/logger.js';
import { asyncHandler } from '../../../router.js';
import { AuthenticatedRequest } from '../../../../middleware/auth.middleware.js';

// GET /chat/rooms/:id/messages - Get messages from a chat room
export const GET = asyncHandler(async (req: Request, res: Response) => {
  try {
    const user = (req as AuthenticatedRequest).user;
    const { id } = req.params;
    const pagination = req.pagination!;

    const result = await ChatService.getRoomMessages(id, user.id, pagination);

    return ResponseHelper.paginated(
      res,
      result.messages,
      pagination.page,
      pagination.limit,
      result.total,
      'Messages retrieved successfully'
    );

  } catch (error) {
    logger.error('Error fetching room messages:', error);
    
    const message = error instanceof Error ? error.message : 'Failed to fetch messages';
    
    if (message.includes('Access denied')) {
      return ResponseHelper.forbidden(res, message);
    }
    
    return ResponseHelper.error(res, message);
  }
});

// POST /chat/rooms/:id/messages - Send a message to a chat room
export const POST = asyncHandler(async (req: Request, res: Response) => {
  try {
    const user = (req as AuthenticatedRequest).user;
    const { id } = req.params;
    const { content, message_type = 'text', reply_to_id } = req.body;

    const message = await ChatService.sendMessage(id, user.id, content, message_type, reply_to_id);

    logger.info('Message sent via API', { message_id: message.id, room_id: id, sender_id: user.id });

    return ResponseHelper.created(res, message, 'Message sent successfully');

  } catch (error) {
    logger.error('Error sending message:', error);
    
    const message = error instanceof Error ? error.message : 'Failed to send message';
    
    if (message.includes('Access denied')) {
      return ResponseHelper.forbidden(res, message);
    }
    
    return ResponseHelper.error(res, message);
  }
});

export const middleware = [
  validateParams(schemas.params.uuid),
  requirePermission('chat.read'),
];

// Override middleware for specific methods
GET.middleware = [
  validateParams(schemas.params.uuid),
  requirePermission('chat.read'),
  paginationMiddleware,
];

POST.middleware = [
  validateParams(schemas.params.uuid),
  requirePermission('chat.write'),
  validateSchema(schemas.chat.sendMessage),
];
