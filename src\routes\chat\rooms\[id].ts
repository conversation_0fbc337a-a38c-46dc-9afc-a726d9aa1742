import { Request, Response } from 'express';
import { ChatService } from '../../../services/chat.service.ts';
import { ResponseHelper } from '../../../utils/response.ts';
import { validateParams, schemas } from '../../../utils/validation.ts';
import { requirePermission } from '../../../middleware/auth.middleware.ts';
import { logger } from '../../../utils/logger.ts';
import { asyncHandler } from '../../router.ts';
import { AuthenticatedRequest } from '../../../middleware/auth.middleware.ts';

// GET /chat/rooms/:id - Get chat room details
export const GET = asyncHandler(async (req: Request, res: Response) => {
  try {
    const user = (req as AuthenticatedRequest).user;
    const { id } = req.params;

    const room = await ChatService.getRoomById(id, user.id);

    if (!room) {
      return ResponseHelper.notFound(res, 'Chat room not found or access denied');
    }

    return ResponseHelper.success(res, room, 'Chat room retrieved successfully');

  } catch (error) {
    logger.error('Error fetching chat room:', error);
    
    const message = error instanceof Error ? error.message : 'Failed to fetch chat room';
    
    if (message.includes('Access denied')) {
      return ResponseHelper.forbidden(res, message);
    }
    
    return ResponseHelper.error(res, message);
  }
});

export const middleware = [
  validateParams(schemas.params.uuid),
  requirePermission('chat.read'),
];
