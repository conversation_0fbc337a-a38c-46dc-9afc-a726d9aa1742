import { Request, Response } from 'express';
import { ChatService } from '../../../services/chat.service.js';
import { ResponseHelper } from '../../../utils/response.js';
import { validateParams, schemas } from '../../../utils/validation.js';
import { requirePermission } from '../../../middleware/auth.middleware.js';
import { logger } from '../../../utils/logger.js';
import { asyncHandler } from '../../router.js';
import { AuthenticatedRequest } from '../../../middleware/auth.middleware.js';

// GET /chat/rooms/:id - Get chat room details
export const GET = asyncHandler(async (req: Request, res: Response) => {
  try {
    const user = (req as AuthenticatedRequest).user;
    const { id } = req.params;

    const room = await ChatService.getRoomById(id, user.id);

    if (!room) {
      return ResponseHelper.notFound(res, 'Chat room not found or access denied');
    }

    return ResponseHelper.success(res, room, 'Chat room retrieved successfully');

  } catch (error) {
    logger.error('Error fetching chat room:', error);
    
    const message = error instanceof Error ? error.message : 'Failed to fetch chat room';
    
    if (message.includes('Access denied')) {
      return ResponseHelper.forbidden(res, message);
    }
    
    return ResponseHelper.error(res, message);
  }
});

export const middleware = [
  validateParams(schemas.params.uuid),
  requirePermission('chat.read'),
];
