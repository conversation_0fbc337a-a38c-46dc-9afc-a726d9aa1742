import { db } from '../config/database.js';
import { logger } from '../utils/logger.js';
import { UserProfile, PaginationQuery } from '../types/index.js';

export class UserService {
  static async getUserById(id: string): Promise<UserProfile | null> {
    try {
      const result = await db.query(
        `SELECT id, email, user_type, first_name, last_name, company_name, phone, 
                avatar_url, is_active, is_verified, created_at, updated_at
         FROM users WHERE id = $1`,
        [id]
      );

      return result.rows.length > 0 ? result.rows[0] : null;
    } catch (error) {
      logger.error('Error fetching user by ID:', error);
      throw error;
    }
  }

  static async getUserByEmail(email: string): Promise<UserProfile | null> {
    try {
      const result = await db.query(
        `SELECT id, email, user_type, first_name, last_name, company_name, phone, 
                avatar_url, is_active, is_verified, created_at, updated_at
         FROM users WHERE email = $1`,
        [email]
      );

      return result.rows.length > 0 ? result.rows[0] : null;
    } catch (error) {
      logger.error('Error fetching user by email:', error);
      throw error;
    }
  }

  static async updateUser(
    id: string,
    updates: {
      first_name?: string;
      last_name?: string;
      company_name?: string;
      phone?: string;
      avatar_url?: string;
    }
  ): Promise<UserProfile> {
    try {
      const setClause = [];
      const values = [];
      let paramIndex = 1;

      for (const [key, value] of Object.entries(updates)) {
        if (value !== undefined) {
          setClause.push(`${key} = $${paramIndex}`);
          values.push(value);
          paramIndex++;
        }
      }

      if (setClause.length === 0) {
        throw new Error('No valid fields to update');
      }

      values.push(id);

      const result = await db.query(
        `UPDATE users 
         SET ${setClause.join(', ')}, updated_at = CURRENT_TIMESTAMP
         WHERE id = $${paramIndex}
         RETURNING id, email, user_type, first_name, last_name, company_name, phone, 
                   avatar_url, is_active, is_verified, created_at, updated_at`,
        values
      );

      if (result.rows.length === 0) {
        throw new Error('User not found');
      }

      logger.info('User updated successfully', { user_id: id });
      return result.rows[0];
    } catch (error) {
      logger.error('Error updating user:', error);
      throw error;
    }
  }

  static async deactivateUser(id: string): Promise<void> {
    try {
      const result = await db.query(
        'UPDATE users SET is_active = false, updated_at = CURRENT_TIMESTAMP WHERE id = $1',
        [id]
      );

      if (result.rowCount === 0) {
        throw new Error('User not found');
      }

      // Revoke all refresh tokens
      await db.query(
        'UPDATE refresh_tokens SET is_revoked = true WHERE user_id = $1',
        [id]
      );

      logger.info('User deactivated successfully', { user_id: id });
    } catch (error) {
      logger.error('Error deactivating user:', error);
      throw error;
    }
  }

  static async getUsers(
    pagination: PaginationQuery,
    filters?: {
      user_type?: 'worker' | 'company';
      is_active?: boolean;
      search?: string;
    }
  ): Promise<{ users: UserProfile[]; total: number }> {
    try {
      const { page = 1, limit = 20, sort_by = 'created_at', sort_order = 'desc' } = pagination;
      const offset = (page - 1) * limit;

      let whereClause = 'WHERE 1=1';
      const values: any[] = [];
      let paramIndex = 1;

      if (filters?.user_type) {
        whereClause += ` AND user_type = $${paramIndex}`;
        values.push(filters.user_type);
        paramIndex++;
      }

      if (filters?.is_active !== undefined) {
        whereClause += ` AND is_active = $${paramIndex}`;
        values.push(filters.is_active);
        paramIndex++;
      }

      if (filters?.search) {
        whereClause += ` AND (
          first_name ILIKE $${paramIndex} OR 
          last_name ILIKE $${paramIndex} OR 
          email ILIKE $${paramIndex} OR 
          company_name ILIKE $${paramIndex}
        )`;
        values.push(`%${filters.search}%`);
        paramIndex++;
      }

      // Get total count
      const countResult = await db.query(
        `SELECT COUNT(*) as total FROM users ${whereClause}`,
        values
      );
      const total = parseInt(countResult.rows[0].total);

      // Get users
      const usersResult = await db.query(
        `SELECT id, email, user_type, first_name, last_name, company_name, phone, 
                avatar_url, is_active, is_verified, created_at, updated_at
         FROM users 
         ${whereClause}
         ORDER BY ${sort_by} ${sort_order.toUpperCase()}
         LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`,
        [...values, limit, offset]
      );

      return {
        users: usersResult.rows,
        total,
      };
    } catch (error) {
      logger.error('Error fetching users:', error);
      throw error;
    }
  }

  static async getUserPermissions(userId: string): Promise<string[]> {
    try {
      const result = await db.query(
        `SELECT p.name
         FROM user_permissions up
         JOIN permissions p ON up.permission_id = p.id
         WHERE up.user_id = $1`,
        [userId]
      );

      return result.rows.map(row => row.name);
    } catch (error) {
      logger.error('Error fetching user permissions:', error);
      throw error;
    }
  }

  static async hasPermission(userId: string, permission: string): Promise<boolean> {
    try {
      const result = await db.query(
        `SELECT 1
         FROM user_permissions up
         JOIN permissions p ON up.permission_id = p.id
         WHERE up.user_id = $1 AND p.name = $2`,
        [userId, permission]
      );

      return result.rows.length > 0;
    } catch (error) {
      logger.error('Error checking user permission:', error);
      throw error;
    }
  }
}
