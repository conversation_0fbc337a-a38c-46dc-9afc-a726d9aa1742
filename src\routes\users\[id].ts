import { Request, Response } from 'express';
import { UserService } from '../../services/user.service.ts';
import { ResponseHelper } from '../../utils/response.ts';
import { validateParams, validateSchema, schemas } from '../../utils/validation.ts';
import { canAccessUser, requireOwnership } from '../../middleware/auth.middleware.ts';
import { logger } from '../../utils/logger.ts';
import { asyncHandler } from '../router.ts';
import { AuthenticatedRequest } from '../../middleware/auth.middleware.ts';

// GET /users/:id - Get user by ID
export const GET = asyncHandler(async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    const user = await UserService.getUserById(id);

    if (!user) {
      return ResponseHelper.notFound(res, 'User not found');
    }

    // Get user permissions if accessing own profile or has admin access
    const currentUser = (req as AuthenticatedRequest).user;
    let permissions: string[] = [];
    
    if (currentUser.id === id || await UserService.hasPermission(currentUser.id, 'admin.users')) {
      permissions = await UserService.getUserPermissions(id);
    }

    return ResponseHelper.success(res, {
      ...user,
      ...(permissions.length > 0 && { permissions }),
    }, 'User retrieved successfully');

  } catch (error) {
    logger.error('Error fetching user:', error);
    
    const message = error instanceof Error ? error.message : 'Failed to fetch user';
    return ResponseHelper.error(res, message);
  }
});

// PUT /users/:id - Update user
export const PUT = asyncHandler(async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const updates = req.body;

    const updatedUser = await UserService.updateUser(id, updates);

    logger.info('User updated successfully', { user_id: id });

    return ResponseHelper.success(res, updatedUser, 'User updated successfully');

  } catch (error) {
    logger.error('Error updating user:', error);
    
    const message = error instanceof Error ? error.message : 'Failed to update user';
    
    if (message.includes('not found')) {
      return ResponseHelper.notFound(res, message);
    }
    
    return ResponseHelper.error(res, message);
  }
});

// DELETE /users/:id - Deactivate user
export const DELETE = asyncHandler(async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    await UserService.deactivateUser(id);

    logger.info('User deactivated successfully', { user_id: id });

    return ResponseHelper.success(res, null, 'User deactivated successfully');

  } catch (error) {
    logger.error('Error deactivating user:', error);
    
    const message = error instanceof Error ? error.message : 'Failed to deactivate user';
    
    if (message.includes('not found')) {
      return ResponseHelper.notFound(res, message);
    }
    
    return ResponseHelper.error(res, message);
  }
});

// Middleware for all routes in this file
export const middleware = [
  validateParams(schemas.params.uuid),
  canAccessUser, // For GET requests
];

// Override middleware for specific methods
GET.middleware = [
  validateParams(schemas.params.uuid),
  canAccessUser,
];

PUT.middleware = [
  validateParams(schemas.params.uuid),
  requireOwnership(),
  validateSchema(schemas.user.update),
];

DELETE.middleware = [
  validateParams(schemas.params.uuid),
  requireOwnership(),
];
