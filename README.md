# CheffUp API

A scalable Node.js API for a multi-user application with real-time chat functionality, built with TypeScript, Express, PostgreSQL, and Socket.IO.

## Features

- **Multi-User System**: Support for two user types - "worker" and "company"
- **JWT Authentication**: Secure token-based authentication with refresh tokens
- **Role-Based Authorization**: Flexible permission system for different access levels
- **Directory-Based Routing**: Automatic route discovery and registration
- **Real-Time Chat**: Socket.IO-based chat system with message persistence
- **Database Migrations**: Automated database schema management
- **Comprehensive Logging**: Winston-based logging with multiple transports
- **Performance Monitoring**: Built-in metrics and health checks
- **Type Safety**: Full TypeScript implementation
- **Security**: Helmet, CORS, rate limiting, and input validation

## Tech Stack

- **Runtime**: Bun
- **Framework**: Express.js
- **Database**: PostgreSQL
- **Real-time**: Socket.IO
- **Authentication**: JWT
- **Validation**: Joi
- **Logging**: Winston
- **Security**: Helmet, CORS, bcryptjs
- **Language**: TypeScript

## Prerequisites

- Bun (latest version)
- PostgreSQL (12+)
- Node.js (18+ for compatibility)

## Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd cheff-up-api
```

2. Install dependencies:
```bash
bun install
```

3. Set up environment variables:
```bash
cp .env.example .env
# Edit .env with your configuration
```

4. Set up the database:
```bash
# Run migrations
bun run migrate

# Seed with sample data (optional)
bun run seed
```

## Environment Variables

```env
# Database Configuration
POSTGRES_USER=your_db_user
POSTGRES_PASSWORD=your_db_password
POSTGRES_DB=cheff-up-db
POSTGRES_PORT=5432
POSTGRES_HOST=localhost
DATABASE_URL=postgresql://user:password@localhost:5432/cheff-up-db

# Server Configuration
PORT=3000
NODE_ENV=development

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d
JWT_REFRESH_EXPIRES_IN=30d

# Client Configuration
CLIENT_URL=http://localhost:3000

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
```

## Usage

### Development
```bash
bun run dev
```

### Production
```bash
bun run build
bun run start
```

### Database Operations
```bash
# Run migrations
bun run migrate

# Seed database
bun run seed
```

## API Endpoints

### Authentication
- `POST /api/auth/register` - Register new user
- `POST /api/auth/login` - Login user
- `POST /api/auth/logout` - Logout user
- `POST /api/auth/refresh` - Refresh access token
- `GET /api/auth/me` - Get current user profile

### Users
- `GET /api/users` - Get all users (admin only)
- `GET /api/users/:id` - Get user by ID
- `PUT /api/users/:id` - Update user
- `DELETE /api/users/:id` - Deactivate user

### Chat
- `GET /api/chat/rooms` - Get user's chat rooms
- `POST /api/chat/rooms` - Create new chat room
- `GET /api/chat/rooms/:id` - Get chat room details
- `GET /api/chat/rooms/:id/messages` - Get room messages
- `POST /api/chat/rooms/:id/messages` - Send message

### System
- `GET /api/health` - Health check
- `GET /api/metrics` - System metrics (admin only)

## Socket.IO Events

### Client to Server
- `join_room` - Join a chat room
- `leave_room` - Leave a chat room
- `send_message` - Send a message
- `mark_read` - Mark messages as read
- `typing_start` - Start typing indicator
- `typing_stop` - Stop typing indicator
- `start_direct_chat` - Initiate direct chat

### Server to Client
- `joined_room` - Confirmation of room join
- `left_room` - Confirmation of room leave
- `new_message` - New message received
- `user_read` - User read messages
- `user_typing` - User typing status
- `user_status_change` - User online/offline status
- `new_direct_chat` - New direct chat initiated
- `error` - Error message

## Database Schema

### Users
- Multi-user support (worker/company)
- Secure password hashing
- Profile information
- Account status tracking

### Authentication
- Refresh token management
- Token expiration handling
- Secure token storage

### Permissions
- Role-based access control
- Granular permissions
- User-permission mapping

### Chat System
- Chat rooms (direct/group)
- Message persistence
- Participant management
- Read status tracking

## Architecture

### Directory-Based Routing
Routes are automatically discovered based on file structure:
```
src/routes/
├── auth/
│   ├── login.ts      # POST /api/auth/login
│   ├── register.ts   # POST /api/auth/register
│   └── me.ts         # GET /api/auth/me
├── users/
│   ├── index.ts      # GET /api/users
│   └── [id].ts       # GET/PUT/DELETE /api/users/:id
└── health.ts         # GET /api/health
```

### Middleware Stack
1. Security (Helmet)
2. CORS
3. Rate Limiting
4. Compression
5. Request Logging
6. Metrics Tracking
7. Body Parsing
8. Authentication (where required)
9. Authorization (where required)
10. Route Handlers
11. Error Handling

### Permission System
- Resource-based permissions (user, chat, admin)
- Action-based permissions (read, write, create, delete, moderate)
- Default permissions assigned based on user type
- Flexible middleware for route protection

## Security Features

- JWT-based authentication with refresh tokens
- Password hashing with bcrypt
- Rate limiting to prevent abuse
- Input validation and sanitization
- CORS configuration
- Security headers with Helmet
- SQL injection prevention
- XSS protection

## Monitoring & Logging

- Comprehensive request/response logging
- Performance metrics tracking
- Error tracking and reporting
- Health check endpoints
- Periodic metrics reporting
- Database query logging

## Testing

```bash
# Run tests (when implemented)
bun test
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

This project is licensed under the MIT License.
