import fs from 'fs/promises';
import path from 'path';
import { db } from '../config/database.ts';
import { logger } from '../utils/logger.ts';

interface Migration {
  filename: string;
  sql: string;
}

class MigrationRunner {
  private migrationsPath: string;

  constructor() {
    this.migrationsPath = path.join(process.cwd(), 'src', 'database', 'migrations');
  }

  async createMigrationsTable(): Promise<void> {
    const sql = `
      CREATE TABLE IF NOT EXISTS migrations (
        id SERIAL PRIMARY KEY,
        filename VARCHAR(255) UNIQUE NOT NULL,
        executed_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      );
    `;
    
    await db.query(sql);
    logger.info('Migrations table created or already exists');
  }

  async getExecutedMigrations(): Promise<string[]> {
    const result = await db.query('SELECT filename FROM migrations ORDER BY id');
    return result.rows.map((row: any) => row.filename);
  }

  async getMigrationFiles(): Promise<Migration[]> {
    try {
      const files = await fs.readdir(this.migrationsPath);
      const sqlFiles = files
        .filter(file => file.endsWith('.sql'))
        .sort();

      const migrations: Migration[] = [];
      
      for (const filename of sqlFiles) {
        const filePath = path.join(this.migrationsPath, filename);
        const sql = await fs.readFile(filePath, 'utf-8');
        migrations.push({ filename, sql });
      }

      return migrations;
    } catch (error) {
      logger.error('Error reading migration files:', error);
      throw error;
    }
  }

  async executeMigration(migration: Migration): Promise<void> {
    await db.transaction(async (client) => {
      // Execute the migration SQL
      await client.query(migration.sql);
      
      // Record the migration as executed
      await client.query(
        'INSERT INTO migrations (filename) VALUES ($1)',
        [migration.filename]
      );
      
      logger.info(`Migration executed: ${migration.filename}`);
    });
  }

  async run(): Promise<void> {
    try {
      logger.info('Starting database migrations...');
      
      // Connect to database
      await db.connect();
      
      // Create migrations table if it doesn't exist
      await this.createMigrationsTable();
      
      // Get executed migrations
      const executedMigrations = await this.getExecutedMigrations();
      
      // Get all migration files
      const allMigrations = await this.getMigrationFiles();
      
      // Filter out already executed migrations
      const pendingMigrations = allMigrations.filter(
        migration => !executedMigrations.includes(migration.filename)
      );
      
      if (pendingMigrations.length === 0) {
        logger.info('No pending migrations to execute');
        return;
      }
      
      logger.info(`Found ${pendingMigrations.length} pending migrations`);
      
      // Execute pending migrations
      for (const migration of pendingMigrations) {
        await this.executeMigration(migration);
      }
      
      logger.info('All migrations completed successfully');
      
    } catch (error) {
      logger.error('Migration failed:', error);
      throw error;
    }
  }

  async rollback(steps: number = 1): Promise<void> {
    try {
      logger.info(`Rolling back ${steps} migration(s)...`);
      
      await db.connect();
      
      const result = await db.query(
        'SELECT filename FROM migrations ORDER BY id DESC LIMIT $1',
        [steps]
      );
      
      if (result.rows.length === 0) {
        logger.info('No migrations to rollback');
        return;
      }
      
      // Note: This is a simple implementation. In a production system,
      // you would want to have separate rollback SQL files
      for (const row of result.rows) {
        await db.query(
          'DELETE FROM migrations WHERE filename = $1',
          [row.filename]
        );
        logger.info(`Rolled back migration: ${row.filename}`);
      }
      
      logger.warn('Note: This rollback only removes migration records. Manual cleanup may be required.');
      
    } catch (error) {
      logger.error('Rollback failed:', error);
      throw error;
    }
  }
}

// Run migrations if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const runner = new MigrationRunner();
  
  const command = process.argv[2];
  
  if (command === 'rollback') {
    const steps = parseInt(process.argv[3] || '1');
    runner.rollback(steps)
      .then(() => process.exit(0))
      .catch(() => process.exit(1));
  } else {
    runner.run()
      .then(() => process.exit(0))
      .catch(() => process.exit(1));
  }
}

export default MigrationRunner;
