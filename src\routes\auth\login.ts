import { Request, Response } from 'express';
import { AuthService } from '../../services/auth.service.ts';
import { ResponseHelper } from '../../utils/response.ts';
import { validateSchema, schemas } from '../../utils/validation.ts';
import { logger } from '../../utils/logger.ts';
import { asyncHandler } from '../router.ts';

export const middleware = [validateSchema(schemas.user.login)];
export const public = true; // This route doesn't require authentication

export const POST = asyncHandler(async (req: Request, res: Response) => {
  try {
    const { email, password } = req.body;

    const result = await AuthService.login(email, password);

    logger.info('User login successful', { 
      user_id: result.user.id, 
      email: result.user.email,
      user_type: result.user.user_type 
    });

    return ResponseHelper.success(res, {
      user: result.user,
      tokens: result.tokens,
    }, 'Login successful');

  } catch (error) {
    logger.error('<PERSON><PERSON> failed:', error);
    
    const message = error instanceof Error ? error.message : '<PERSON><PERSON> failed';
    return ResponseHelper.error(res, message, undefined, 401);
  }
});
