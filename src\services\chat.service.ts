import { db } from '../config/database.ts';
import { logger } from '../utils/logger.ts';
import { ChatRoom, ChatMessage, ChatParticipant, PaginationQuery } from '../types/index.ts';

export class ChatService {
  static async createRoom(
    createdBy: string,
    type: 'direct' | 'group',
    participantIds: string[],
    name?: string
  ): Promise<ChatRoom> {
    try {
      return await db.transaction(async (client) => {
        // Create the room
        const roomResult = await client.query(
          `INSERT INTO chat_rooms (name, type, created_by)
           VALUES ($1, $2, $3)
           RETURNING id, name, type, created_by, created_at, updated_at`,
          [name || null, type, createdBy]
        );

        const room: ChatRoom = roomResult.rows[0];

        // Add creator as participant
        if (!participantIds.includes(createdBy)) {
          participantIds.push(createdBy);
        }

        // Add all participants
        for (const participantId of participantIds) {
          await client.query(
            `INSERT INTO chat_participants (room_id, user_id)
             VALUES ($1, $2)
             ON CONFLICT (room_id, user_id) DO NOTHING`,
            [room.id, participantId]
          );
        }

        logger.info('Chat room created', { room_id: room.id, type, participants: participantIds.length });
        return room;
      });
    } catch (error) {
      logger.error('Error creating chat room:', error);
      throw error;
    }
  }

  static async getUserRooms(userId: string, pagination: PaginationQuery): Promise<{ rooms: ChatRoom[]; total: number }> {
    try {
      const { page = 1, limit = 20, sort_by = 'updated_at', sort_order = 'desc' } = pagination;
      const offset = (page - 1) * limit;

      // Get total count
      const countResult = await db.query(
        `SELECT COUNT(DISTINCT cr.id) as total
         FROM chat_rooms cr
         JOIN chat_participants cp ON cr.id = cp.room_id
         WHERE cp.user_id = $1 AND cp.is_active = true`,
        [userId]
      );
      const total = parseInt(countResult.rows[0].total);

      // Get rooms
      const roomsResult = await db.query(
        `SELECT DISTINCT cr.id, cr.name, cr.type, cr.created_by, cr.created_at, cr.updated_at,
                (SELECT COUNT(*) FROM chat_participants WHERE room_id = cr.id AND is_active = true) as participant_count,
                (SELECT COUNT(*) FROM chat_messages WHERE room_id = cr.id) as message_count
         FROM chat_rooms cr
         JOIN chat_participants cp ON cr.id = cp.room_id
         WHERE cp.user_id = $1 AND cp.is_active = true
         ORDER BY cr.${sort_by} ${sort_order.toUpperCase()}
         LIMIT $2 OFFSET $3`,
        [userId, limit, offset]
      );

      return {
        rooms: roomsResult.rows,
        total,
      };
    } catch (error) {
      logger.error('Error fetching user rooms:', error);
      throw error;
    }
  }

  static async getRoomById(roomId: string, userId: string): Promise<ChatRoom | null> {
    try {
      // Check if user is a participant
      const participantCheck = await db.query(
        'SELECT 1 FROM chat_participants WHERE room_id = $1 AND user_id = $2 AND is_active = true',
        [roomId, userId]
      );

      if (participantCheck.rows.length === 0) {
        throw new Error('Access denied: not a participant in this room');
      }

      const result = await db.query(
        `SELECT cr.*, 
                (SELECT COUNT(*) FROM chat_participants WHERE room_id = cr.id AND is_active = true) as participant_count,
                (SELECT COUNT(*) FROM chat_messages WHERE room_id = cr.id) as message_count
         FROM chat_rooms cr
         WHERE cr.id = $1`,
        [roomId]
      );

      return result.rows.length > 0 ? result.rows[0] : null;
    } catch (error) {
      logger.error('Error fetching room:', error);
      throw error;
    }
  }

  static async sendMessage(
    roomId: string,
    senderId: string,
    content: string,
    messageType: 'text' | 'image' | 'file' = 'text',
    replyToId?: string
  ): Promise<ChatMessage> {
    try {
      // Verify user is a participant
      const participantCheck = await db.query(
        'SELECT 1 FROM chat_participants WHERE room_id = $1 AND user_id = $2 AND is_active = true',
        [roomId, senderId]
      );

      if (participantCheck.rows.length === 0) {
        throw new Error('Access denied: not a participant in this room');
      }

      const result = await db.query(
        `INSERT INTO chat_messages (room_id, sender_id, content, message_type, reply_to_id)
         VALUES ($1, $2, $3, $4, $5)
         RETURNING id, room_id, sender_id, content, message_type, file_url, file_name, file_size,
                   reply_to_id, is_edited, edited_at, created_at`,
        [roomId, senderId, content, messageType, replyToId || null]
      );

      const message: ChatMessage = result.rows[0];

      logger.info('Message sent', { message_id: message.id, room_id: roomId, sender_id: senderId });
      return message;
    } catch (error) {
      logger.error('Error sending message:', error);
      throw error;
    }
  }

  static async getRoomMessages(
    roomId: string,
    userId: string,
    pagination: PaginationQuery
  ): Promise<{ messages: ChatMessage[]; total: number }> {
    try {
      // Verify user is a participant
      const participantCheck = await db.query(
        'SELECT 1 FROM chat_participants WHERE room_id = $1 AND user_id = $2 AND is_active = true',
        [roomId, userId]
      );

      if (participantCheck.rows.length === 0) {
        throw new Error('Access denied: not a participant in this room');
      }

      const { page = 1, limit = 50, sort_order = 'desc' } = pagination;
      const offset = (page - 1) * limit;

      // Get total count
      const countResult = await db.query(
        'SELECT COUNT(*) as total FROM chat_messages WHERE room_id = $1',
        [roomId]
      );
      const total = parseInt(countResult.rows[0].total);

      // Get messages with sender info
      const messagesResult = await db.query(
        `SELECT cm.id, cm.room_id, cm.sender_id, cm.content, cm.message_type, cm.file_url,
                cm.file_name, cm.file_size, cm.reply_to_id, cm.is_edited, cm.edited_at, cm.created_at,
                u.first_name, u.last_name, u.avatar_url, u.user_type,
                rt.content as reply_to_content, rt.sender_id as reply_to_sender_id
         FROM chat_messages cm
         JOIN users u ON cm.sender_id = u.id
         LEFT JOIN chat_messages rt ON cm.reply_to_id = rt.id
         WHERE cm.room_id = $1
         ORDER BY cm.created_at ${sort_order.toUpperCase()}
         LIMIT $2 OFFSET $3`,
        [roomId, limit, offset]
      );

      return {
        messages: messagesResult.rows,
        total,
      };
    } catch (error) {
      logger.error('Error fetching room messages:', error);
      throw error;
    }
  }

  static async addParticipant(roomId: string, userId: string, addedBy: string): Promise<void> {
    try {
      // Check if the person adding has permission (room creator or admin)
      const roomCheck = await db.query(
        'SELECT created_by FROM chat_rooms WHERE id = $1',
        [roomId]
      );

      if (roomCheck.rows.length === 0) {
        throw new Error('Room not found');
      }

      const room = roomCheck.rows[0];
      if (room.created_by !== addedBy) {
        // Check if user has admin permissions
        const hasAdminPermission = await db.query(
          `SELECT 1 FROM user_permissions up
           JOIN permissions p ON up.permission_id = p.id
           WHERE up.user_id = $1 AND p.name = 'chat.moderate'`,
          [addedBy]
        );

        if (hasAdminPermission.rows.length === 0) {
          throw new Error('Permission denied: cannot add participants');
        }
      }

      await db.query(
        `INSERT INTO chat_participants (room_id, user_id)
         VALUES ($1, $2)
         ON CONFLICT (room_id, user_id) DO UPDATE SET is_active = true, joined_at = CURRENT_TIMESTAMP`,
        [roomId, userId]
      );

      logger.info('Participant added to room', { room_id: roomId, user_id: userId, added_by: addedBy });
    } catch (error) {
      logger.error('Error adding participant:', error);
      throw error;
    }
  }

  static async removeParticipant(roomId: string, userId: string, removedBy: string): Promise<void> {
    try {
      // Users can remove themselves, or room creator/admin can remove others
      if (userId !== removedBy) {
        const roomCheck = await db.query(
          'SELECT created_by FROM chat_rooms WHERE id = $1',
          [roomId]
        );

        if (roomCheck.rows.length === 0) {
          throw new Error('Room not found');
        }

        const room = roomCheck.rows[0];
        if (room.created_by !== removedBy) {
          // Check if user has admin permissions
          const hasAdminPermission = await db.query(
            `SELECT 1 FROM user_permissions up
             JOIN permissions p ON up.permission_id = p.id
             WHERE up.user_id = $1 AND p.name = 'chat.moderate'`,
            [removedBy]
          );

          if (hasAdminPermission.rows.length === 0) {
            throw new Error('Permission denied: cannot remove participants');
          }
        }
      }

      await db.query(
        'UPDATE chat_participants SET is_active = false WHERE room_id = $1 AND user_id = $2',
        [roomId, userId]
      );

      logger.info('Participant removed from room', { room_id: roomId, user_id: userId, removed_by: removedBy });
    } catch (error) {
      logger.error('Error removing participant:', error);
      throw error;
    }
  }

  static async updateLastRead(roomId: string, userId: string): Promise<void> {
    try {
      await db.query(
        'UPDATE chat_participants SET last_read_at = CURRENT_TIMESTAMP WHERE room_id = $1 AND user_id = $2',
        [roomId, userId]
      );
    } catch (error) {
      logger.error('Error updating last read:', error);
      throw error;
    }
  }

  static async getDirectRoom(user1Id: string, user2Id: string): Promise<ChatRoom | null> {
    try {
      const result = await db.query(
        `SELECT DISTINCT cr.*
         FROM chat_rooms cr
         JOIN chat_participants cp1 ON cr.id = cp1.room_id
         JOIN chat_participants cp2 ON cr.id = cp2.room_id
         WHERE cr.type = 'direct'
           AND cp1.user_id = $1 AND cp1.is_active = true
           AND cp2.user_id = $2 AND cp2.is_active = true
           AND (SELECT COUNT(*) FROM chat_participants WHERE room_id = cr.id AND is_active = true) = 2`,
        [user1Id, user2Id]
      );

      return result.rows.length > 0 ? result.rows[0] : null;
    } catch (error) {
      logger.error('Error finding direct room:', error);
      throw error;
    }
  }
}
