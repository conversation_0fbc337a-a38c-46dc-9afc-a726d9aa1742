import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { v4 as uuidv4 } from 'uuid';
import { db } from '../config/database.js';
import { config } from '../config/index.js';
import { logger } from '../utils/logger.js';
import { User, UserProfile, AuthTokens, JWTPayload, RefreshToken } from '../types/index.js';

export class AuthService {
  private static readonly SALT_ROUNDS = 12;

  static async hashPassword(password: string): Promise<string> {
    return bcrypt.hash(password, this.SALT_ROUNDS);
  }

  static async comparePassword(password: string, hash: string): Promise<boolean> {
    return bcrypt.compare(password, hash);
  }

  static generateTokens(user: UserProfile): AuthTokens {
    const payload: Omit<JWTPayload, 'iat' | 'exp'> = {
      user_id: user.id,
      email: user.email,
      user_type: user.user_type,
    };

    const accessToken = jwt.sign(payload, config.jwt_secret, {
      expiresIn: config.jwt_expires_in,
    });

    const refreshToken = jwt.sign(
      { user_id: user.id },
      config.jwt_secret,
      { expiresIn: config.jwt_refresh_expires_in }
    );

    // Calculate expires_in in seconds
    const decoded = jwt.decode(accessToken) as JWTPayload;
    const expiresIn = decoded.exp - decoded.iat;

    return {
      access_token: accessToken,
      refresh_token: refreshToken,
      expires_in: expiresIn,
    };
  }

  static async verifyToken(token: string): Promise<JWTPayload> {
    try {
      return jwt.verify(token, config.jwt_secret) as JWTPayload;
    } catch (error) {
      throw new Error('Invalid token');
    }
  }

  static async register(userData: {
    email: string;
    password: string;
    user_type: 'worker' | 'company';
    first_name: string;
    last_name: string;
    company_name?: string;
    phone?: string;
  }): Promise<{ user: UserProfile; tokens: AuthTokens }> {
    try {
      // Check if user already exists
      const existingUser = await db.query(
        'SELECT id FROM users WHERE email = $1',
        [userData.email]
      );

      if (existingUser.rows.length > 0) {
        throw new Error('User already exists with this email');
      }

      // Hash password
      const passwordHash = await this.hashPassword(userData.password);

      // Create user
      const result = await db.query(
        `INSERT INTO users (email, password_hash, user_type, first_name, last_name, company_name, phone)
         VALUES ($1, $2, $3, $4, $5, $6, $7)
         RETURNING id, email, user_type, first_name, last_name, company_name, phone, avatar_url, 
                   is_active, is_verified, created_at, updated_at`,
        [
          userData.email,
          passwordHash,
          userData.user_type,
          userData.first_name,
          userData.last_name,
          userData.company_name || null,
          userData.phone || null,
        ]
      );

      const user: UserProfile = result.rows[0];

      // Generate tokens
      const tokens = this.generateTokens(user);

      // Store refresh token
      await this.storeRefreshToken(user.id, tokens.refresh_token);

      // Grant default permissions based on user type
      await this.grantDefaultPermissions(user.id, user.user_type);

      logger.info('User registered successfully', { user_id: user.id, email: user.email });

      return { user, tokens };
    } catch (error) {
      logger.error('Registration failed:', error);
      throw error;
    }
  }

  static async login(email: string, password: string): Promise<{ user: UserProfile; tokens: AuthTokens }> {
    try {
      // Get user with password hash
      const result = await db.query(
        `SELECT id, email, password_hash, user_type, first_name, last_name, company_name, phone, 
                avatar_url, is_active, is_verified, created_at, updated_at
         FROM users WHERE email = $1`,
        [email]
      );

      if (result.rows.length === 0) {
        throw new Error('Invalid credentials');
      }

      const userData = result.rows[0];

      // Check if user is active
      if (!userData.is_active) {
        throw new Error('Account is deactivated');
      }

      // Verify password
      const isValidPassword = await this.comparePassword(password, userData.password_hash);
      if (!isValidPassword) {
        throw new Error('Invalid credentials');
      }

      // Remove password hash from user object
      const { password_hash, ...user } = userData;

      // Generate tokens
      const tokens = this.generateTokens(user);

      // Store refresh token
      await this.storeRefreshToken(user.id, tokens.refresh_token);

      logger.info('User logged in successfully', { user_id: user.id, email: user.email });

      return { user, tokens };
    } catch (error) {
      logger.error('Login failed:', error);
      throw error;
    }
  }

  static async refreshToken(refreshToken: string): Promise<AuthTokens> {
    try {
      // Verify refresh token
      const decoded = jwt.verify(refreshToken, config.jwt_secret) as { user_id: string };

      // Check if refresh token exists and is not revoked
      const tokenHash = await bcrypt.hash(refreshToken, 1);
      const tokenResult = await db.query(
        'SELECT id FROM refresh_tokens WHERE user_id = $1 AND is_revoked = false AND expires_at > NOW()',
        [decoded.user_id]
      );

      if (tokenResult.rows.length === 0) {
        throw new Error('Invalid refresh token');
      }

      // Get user data
      const userResult = await db.query(
        `SELECT id, email, user_type, first_name, last_name, company_name, phone, 
                avatar_url, is_active, is_verified, created_at, updated_at
         FROM users WHERE id = $1 AND is_active = true`,
        [decoded.user_id]
      );

      if (userResult.rows.length === 0) {
        throw new Error('User not found or inactive');
      }

      const user: UserProfile = userResult.rows[0];

      // Generate new tokens
      const tokens = this.generateTokens(user);

      // Revoke old refresh token and store new one
      await db.transaction(async (client) => {
        await client.query(
          'UPDATE refresh_tokens SET is_revoked = true WHERE user_id = $1',
          [user.id]
        );
        await this.storeRefreshToken(user.id, tokens.refresh_token);
      });

      return tokens;
    } catch (error) {
      logger.error('Token refresh failed:', error);
      throw error;
    }
  }

  static async logout(userId: string): Promise<void> {
    try {
      await db.query(
        'UPDATE refresh_tokens SET is_revoked = true WHERE user_id = $1',
        [userId]
      );
      logger.info('User logged out successfully', { user_id: userId });
    } catch (error) {
      logger.error('Logout failed:', error);
      throw error;
    }
  }

  private static async storeRefreshToken(userId: string, refreshToken: string): Promise<void> {
    const tokenHash = await bcrypt.hash(refreshToken, 1);
    const decoded = jwt.decode(refreshToken) as { exp: number };
    const expiresAt = new Date(decoded.exp * 1000);

    await db.query(
      'INSERT INTO refresh_tokens (user_id, token_hash, expires_at) VALUES ($1, $2, $3)',
      [userId, tokenHash, expiresAt]
    );
  }

  private static async grantDefaultPermissions(userId: string, userType: 'worker' | 'company'): Promise<void> {
    const defaultPermissions = [
      'user.read',
      'user.update',
      'chat.create',
      'chat.read',
      'chat.write',
    ];

    // Companies get additional permissions
    if (userType === 'company') {
      defaultPermissions.push('chat.moderate');
    }

    for (const permissionName of defaultPermissions) {
      await db.query(
        `INSERT INTO user_permissions (user_id, permission_id, granted_by)
         SELECT $1, p.id, $1
         FROM permissions p
         WHERE p.name = $2
         ON CONFLICT (user_id, permission_id) DO NOTHING`,
        [userId, permissionName]
      );
    }
  }
}
