import { Request, Response, NextFunction } from 'express';
import { AuthService } from '../services/auth.service.ts';
import { UserService } from '../services/user.service.ts';
import { ResponseHelper } from '../utils/response.ts';
import { logger } from '../utils/logger.ts';
import { JWTPayload } from '../types/index.ts';

export interface AuthenticatedRequest extends Request {
  user: {
    id: string;
    email: string;
    user_type: 'worker' | 'company';
  };
}

export const authenticate = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return ResponseHelper.unauthorized(res, 'Access token required');
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix

    try {
      const decoded: JWTPayload = await AuthService.verifyToken(token);
      
      // Get fresh user data to ensure user is still active
      const user = await UserService.getUserById(decoded.user_id);
      
      if (!user || !user.is_active) {
        return ResponseHelper.unauthorized(res, 'User account is inactive');
      }

      // Attach user info to request
      req.user = {
        id: user.id,
        email: user.email,
        user_type: user.user_type,
      };

      next();
    } catch (tokenError) {
      return ResponseHelper.unauthorized(res, 'Invalid or expired token');
    }
  } catch (error) {
    logger.error('Authentication middleware error:', error);
    return ResponseHelper.serverError(res, 'Authentication failed');
  }
};

export const requirePermission = (permission: string) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      const user = (req as AuthenticatedRequest).user;
      
      if (!user) {
        return ResponseHelper.unauthorized(res, 'Authentication required');
      }

      const hasPermission = await UserService.hasPermission(user.id, permission);
      
      if (!hasPermission) {
        return ResponseHelper.forbidden(res, `Permission required: ${permission}`);
      }

      next();
    } catch (error) {
      logger.error('Permission check error:', error);
      return ResponseHelper.serverError(res, 'Permission check failed');
    }
  };
};

export const requireUserType = (userType: 'worker' | 'company' | ('worker' | 'company')[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      const user = (req as AuthenticatedRequest).user;
      
      if (!user) {
        return ResponseHelper.unauthorized(res, 'Authentication required');
      }

      const allowedTypes = Array.isArray(userType) ? userType : [userType];
      
      if (!allowedTypes.includes(user.user_type)) {
        return ResponseHelper.forbidden(res, `Access restricted to: ${allowedTypes.join(', ')}`);
      }

      next();
    } catch (error) {
      logger.error('User type check error:', error);
      return ResponseHelper.serverError(res, 'User type check failed');
    }
  };
};

export const requireOwnership = (resourceIdParam: string = 'id') => {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      const user = (req as AuthenticatedRequest).user;
      const resourceId = req.params[resourceIdParam];
      
      if (!user) {
        return ResponseHelper.unauthorized(res, 'Authentication required');
      }

      // Allow if user is accessing their own resource
      if (user.id === resourceId) {
        return next();
      }

      // Check if user has admin permissions
      UserService.hasPermission(user.id, 'admin.users')
        .then(hasAdminPermission => {
          if (hasAdminPermission) {
            return next();
          }
          return ResponseHelper.forbidden(res, 'Access denied: insufficient permissions');
        })
        .catch(error => {
          logger.error('Ownership check error:', error);
          return ResponseHelper.serverError(res, 'Ownership check failed');
        });
    } catch (error) {
      logger.error('Ownership middleware error:', error);
      return ResponseHelper.serverError(res, 'Ownership check failed');
    }
  };
};

export const optionalAuth = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return next(); // Continue without authentication
    }

    const token = authHeader.substring(7);

    try {
      const decoded: JWTPayload = await AuthService.verifyToken(token);
      const user = await UserService.getUserById(decoded.user_id);
      
      if (user && user.is_active) {
        req.user = {
          id: user.id,
          email: user.email,
          user_type: user.user_type,
        };
      }
    } catch (tokenError) {
      // Invalid token, but continue without authentication
      logger.debug('Optional auth failed, continuing without authentication:', tokenError);
    }

    next();
  } catch (error) {
    logger.error('Optional authentication middleware error:', error);
    next(); // Continue even if there's an error
  }
};

// Middleware to check if user can access another user's data
export const canAccessUser = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const currentUser = (req as AuthenticatedRequest).user;
    const targetUserId = req.params.id;
    
    if (!currentUser) {
      return ResponseHelper.unauthorized(res, 'Authentication required');
    }

    // Users can always access their own data
    if (currentUser.id === targetUserId) {
      return next();
    }

    // Check if user has admin permissions
    const hasAdminPermission = await UserService.hasPermission(currentUser.id, 'admin.users');
    if (hasAdminPermission) {
      return next();
    }

    // Companies can access worker data (for hiring purposes)
    if (currentUser.user_type === 'company') {
      const targetUser = await UserService.getUserById(targetUserId);
      if (targetUser && targetUser.user_type === 'worker') {
        return next();
      }
    }

    return ResponseHelper.forbidden(res, 'Access denied');
  } catch (error) {
    logger.error('User access check error:', error);
    return ResponseHelper.serverError(res, 'Access check failed');
  }
};
