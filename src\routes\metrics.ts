import { Request, Response } from 'express';
import { monitoring } from '../utils/monitoring.ts';
import { ResponseHelper } from '../utils/response.ts';
import { requirePermission } from '../middleware/auth.middleware.ts';
import { asyncHandler } from './router.ts';

export const middleware = [requirePermission('admin.users')];

export const GET = asyncHandler(async (req: Request, res: Response) => {
  const metrics = monitoring.getMetrics();
  const health = monitoring.getHealthStatus();

  return ResponseHelper.success(res, {
    ...metrics,
    health,
  }, 'Metrics retrieved successfully');
});
