import { Request, Response } from 'express';
import { monitoring } from '../utils/monitoring.js';
import { ResponseHelper } from '../utils/response.js';
import { requirePermission } from '../middleware/auth.middleware.js';
import { asyncHandler } from './router.js';

export const middleware = [requirePermission('admin.users')];

export const GET = asyncHandler(async (req: Request, res: Response) => {
  const metrics = monitoring.getMetrics();
  const health = monitoring.getHealthStatus();

  return ResponseHelper.success(res, {
    ...metrics,
    health,
  }, 'Metrics retrieved successfully');
});
