import { Request, Response } from 'express';
import { AuthService } from '../../services/auth.service.ts';
import { ResponseHelper } from '../../utils/response.ts';
import { validateSchema, schemas } from '../../utils/validation.ts';
import { logger } from '../../utils/logger.ts';
import { asyncHandler } from '../router.ts';

export const middleware = [validateSchema(schemas.user.register)];
export const public = true; // This route doesn't require authentication

export const POST = asyncHandler(async (req: Request, res: Response) => {
  try {
    const userData = req.body;

    const result = await AuthService.register(userData);

    logger.info('User registration successful', { 
      user_id: result.user.id, 
      email: result.user.email,
      user_type: result.user.user_type 
    });

    return ResponseHelper.created(res, {
      user: result.user,
      tokens: result.tokens,
    }, 'Registration successful');

  } catch (error) {
    logger.error('Registration failed:', error);
    
    const message = error instanceof Error ? error.message : 'Registration failed';
    
    // Handle specific error cases
    if (message.includes('already exists')) {
      return ResponseHelper.conflict(res, message);
    }
    
    return ResponseHelper.error(res, message);
  }
});
