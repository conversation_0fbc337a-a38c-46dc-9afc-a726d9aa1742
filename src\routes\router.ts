import { Router, Request, Response, NextFunction } from 'express';
import fs from 'fs/promises';
import path from 'path';
import { logger } from '../utils/logger.js';
import { authenticate } from '../middleware/auth.middleware.js';

interface RouteModule {
  default?: any;
  GET?: Function;
  POST?: Function;
  PUT?: Function;
  DELETE?: Function;
  PATCH?: Function;
  middleware?: Function[];
  public?: boolean; // If true, skips authentication
}

interface RouteInfo {
  method: string;
  path: string;
  handler: Function;
  middleware: Function[];
  isPublic: boolean;
}

export class AutoRouter {
  private router: Router;
  private routesPath: string;
  private publicRoutes: Set<string> = new Set();

  constructor(routesPath: string) {
    this.router = Router();
    this.routesPath = routesPath;
  }

  async initialize(): Promise<Router> {
    try {
      await this.loadRoutes(this.routesPath);
      logger.info('Auto-router initialized successfully');
      return this.router;
    } catch (error) {
      logger.error('Failed to initialize auto-router:', error);
      throw error;
    }
  }

  private async loadRoutes(dirPath: string, basePath: string = ''): Promise<void> {
    try {
      const entries = await fs.readdir(dirPath, { withFileTypes: true });

      for (const entry of entries) {
        const fullPath = path.join(dirPath, entry.name);
        
        if (entry.isDirectory()) {
          // Recursively load routes from subdirectories
          const subPath = path.join(basePath, entry.name);
          await this.loadRoutes(fullPath, subPath);
        } else if (entry.isFile() && (entry.name.endsWith('.ts') || entry.name.endsWith('.js'))) {
          await this.loadRouteFile(fullPath, basePath, entry.name);
        }
      }
    } catch (error) {
      logger.error(`Error loading routes from ${dirPath}:`, error);
    }
  }

  private async loadRouteFile(filePath: string, basePath: string, fileName: string): Promise<void> {
    try {
      // Skip non-route files
      if (fileName.startsWith('_') || fileName.includes('.test.') || fileName.includes('.spec.')) {
        return;
      }

      const module: RouteModule = await import(filePath);
      const routePath = this.buildRoutePath(basePath, fileName);

      // Extract route information
      const routes = this.extractRoutes(module, routePath);

      // Register routes
      for (const route of routes) {
        this.registerRoute(route);
      }

      logger.debug(`Loaded routes from ${filePath}`, { routePath, methods: routes.map(r => r.method) });
    } catch (error) {
      logger.error(`Error loading route file ${filePath}:`, error);
    }
  }

  private buildRoutePath(basePath: string, fileName: string): string {
    // Remove file extension
    let routeName = fileName.replace(/\.(ts|js)$/, '');
    
    // Handle index files
    if (routeName === 'index') {
      routeName = '';
    }

    // Handle dynamic routes (files with [param] syntax)
    routeName = routeName.replace(/\[([^\]]+)\]/g, ':$1');

    // Build full path
    let fullPath = path.join(basePath, routeName).replace(/\\/g, '/');
    
    // Ensure path starts with /
    if (!fullPath.startsWith('/')) {
      fullPath = '/' + fullPath;
    }

    // Remove trailing slash unless it's the root
    if (fullPath.length > 1 && fullPath.endsWith('/')) {
      fullPath = fullPath.slice(0, -1);
    }

    return fullPath;
  }

  private extractRoutes(module: RouteModule, routePath: string): RouteInfo[] {
    const routes: RouteInfo[] = [];
    const methods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'];

    for (const method of methods) {
      const handler = module[method as keyof RouteModule];
      if (typeof handler === 'function') {
        routes.push({
          method,
          path: routePath,
          handler,
          middleware: module.middleware || [],
          isPublic: module.public || false,
        });
      }
    }

    // Handle default export
    if (module.default && typeof module.default === 'function') {
      routes.push({
        method: 'GET',
        path: routePath,
        handler: module.default,
        middleware: module.middleware || [],
        isPublic: module.public || false,
      });
    }

    return routes;
  }

  private registerRoute(route: RouteInfo): void {
    const { method, path, handler, middleware, isPublic } = route;
    
    // Build middleware chain
    const middlewareChain: Function[] = [];
    
    // Add authentication middleware unless route is public
    if (!isPublic) {
      middlewareChain.push(authenticate);
    } else {
      this.publicRoutes.add(`${method} ${path}`);
    }
    
    // Add route-specific middleware
    middlewareChain.push(...middleware);
    
    // Add the handler
    middlewareChain.push(handler);

    // Register the route
    switch (method.toLowerCase()) {
      case 'get':
        this.router.get(path, ...middlewareChain);
        break;
      case 'post':
        this.router.post(path, ...middlewareChain);
        break;
      case 'put':
        this.router.put(path, ...middlewareChain);
        break;
      case 'delete':
        this.router.delete(path, ...middlewareChain);
        break;
      case 'patch':
        this.router.patch(path, ...middlewareChain);
        break;
    }

    logger.debug(`Registered route: ${method} ${path}`, { 
      isPublic, 
      middlewareCount: middleware.length 
    });
  }

  getPublicRoutes(): string[] {
    return Array.from(this.publicRoutes);
  }

  // Method to manually add routes (for special cases)
  addRoute(
    method: string,
    path: string,
    handler: Function,
    middleware: Function[] = [],
    isPublic: boolean = false
  ): void {
    this.registerRoute({
      method: method.toUpperCase(),
      path,
      handler,
      middleware,
      isPublic,
    });
  }
}

// Helper function to create route handlers with error handling
export const createHandler = (handler: Function) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      await handler(req, res, next);
    } catch (error) {
      next(error);
    }
  };
};

// Helper function to create async route handlers
export const asyncHandler = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};
