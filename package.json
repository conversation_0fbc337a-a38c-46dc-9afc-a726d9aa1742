{"name": "cheff-up-api", "version": "1.0.0", "description": "Scalable Node.js API for multi-user application with chat system", "main": "dist/server.js", "type": "module", "private": true, "scripts": {"build": "bun build src/server.ts --outdir dist --target node", "start": "node dist/server.js", "dev": "bun --watch src/server.ts", "dev:debug": "bun --inspect --watch src/server.ts", "migrate": "bun src/database/migrate.ts", "seed": "bun src/database/seed.ts", "test": "echo \"Error: no test specified\" && exit 1"}, "devDependencies": {"@types/bcryptjs": "^3.0.0", "@types/bun": "latest", "@types/compression": "^1.8.1", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jsonwebtoken": "^9.0.10", "@types/morgan": "^1.9.10", "@types/node": "^24.5.2", "@types/pg": "^8.15.5", "@types/uuid": "^11.0.0", "concurrently": "^9.2.1", "nodemon": "^3.1.10"}, "peerDependencies": {"typescript": "^5"}, "dependencies": {"bcryptjs": "^3.0.2", "compression": "^1.8.1", "cors": "^2.8.5", "dotenv": "^17.2.2", "express": "^5.1.0", "express-rate-limit": "^8.1.0", "express-validator": "^7.2.1", "helmet": "^8.1.0", "joi": "^18.0.1", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.1", "pg": "^8.16.3", "socket.io": "^4.8.1", "uuid": "^13.0.0", "winston": "^3.17.0"}}