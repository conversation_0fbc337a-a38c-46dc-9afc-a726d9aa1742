export interface User {
  id: string;
  email: string;
  password_hash: string;
  user_type: 'worker' | 'company';
  first_name: string;
  last_name: string;
  company_name?: string;
  phone?: string;
  avatar_url?: string;
  is_active: boolean;
  is_verified: boolean;
  created_at: Date;
  updated_at: Date;
}

export interface UserProfile {
  id: string;
  email: string;
  user_type: 'worker' | 'company';
  first_name: string;
  last_name: string;
  company_name?: string;
  phone?: string;
  avatar_url?: string;
  is_active: boolean;
  is_verified: boolean;
  created_at: Date;
  updated_at: Date;
}

export interface AuthTokens {
  access_token: string;
  refresh_token: string;
  expires_in: number;
}

export interface JWTPayload {
  user_id: string;
  email: string;
  user_type: 'worker' | 'company';
  iat: number;
  exp: number;
}

export interface RefreshToken {
  id: string;
  user_id: string;
  token_hash: string;
  expires_at: Date;
  created_at: Date;
  is_revoked: boolean;
}

export interface Permission {
  id: string;
  name: string;
  description: string;
  resource: string;
  action: string;
}

export interface UserPermission {
  user_id: string;
  permission_id: string;
  granted_at: Date;
  granted_by: string;
}

export interface ChatRoom {
  id: string;
  name?: string;
  type: 'direct' | 'group';
  created_by: string;
  created_at: Date;
  updated_at: Date;
}

export interface ChatParticipant {
  room_id: string;
  user_id: string;
  joined_at: Date;
  last_read_at?: Date;
  is_active: boolean;
}

export interface ChatMessage {
  id: string;
  room_id: string;
  sender_id: string;
  content: string;
  message_type: 'text' | 'image' | 'file' | 'system';
  file_url?: string;
  file_name?: string;
  file_size?: number;
  reply_to_id?: string;
  is_edited: boolean;
  edited_at?: Date;
  created_at: Date;
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  errors?: string[];
  pagination?: {
    page: number;
    limit: number;
    total: number;
    total_pages: number;
  };
}

export interface PaginationQuery {
  page?: number;
  limit?: number;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
}

export interface DatabaseConfig {
  host: string;
  port: number;
  database: string;
  username: string;
  password: string;
  ssl?: boolean;
  max_connections?: number;
  idle_timeout?: number;
}

export interface ServerConfig {
  port: number;
  node_env: string;
  client_url: string;
  jwt_secret: string;
  jwt_expires_in: string;
  jwt_refresh_expires_in: string;
  rate_limit_window_ms: number;
  rate_limit_max_requests: number;
}

// Request extensions
declare global {
  namespace Express {
    interface Request {
      user?: UserProfile;
      pagination?: PaginationQuery;
    }
  }
}

export interface RouteHandler {
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  path: string;
  handler: Function;
  middleware?: Function[];
  public?: boolean; // If true, skips authentication
}

export interface SocketUser {
  user_id: string;
  socket_id: string;
  user_type: 'worker' | 'company';
  connected_at: Date;
}
