import { Request, Response } from 'express';
import { AuthService } from '../../services/auth.service.ts';
import { ResponseHelper } from '../../utils/response.ts';
import { logger } from '../../utils/logger.ts';
import { asyncHandler } from '../router.ts';
import { AuthenticatedRequest } from '../../middleware/auth.middleware.ts';

export const POST = asyncHandler(async (req: Request, res: Response) => {
  try {
    const user = (req as AuthenticatedRequest).user;

    await AuthService.logout(user.id);

    logger.info('User logout successful', { user_id: user.id });

    return ResponseHelper.success(res, null, 'Logout successful');

  } catch (error) {
    logger.error('Logout failed:', error);
    
    const message = error instanceof Error ? error.message : 'Logout failed';
    return ResponseHelper.error(res, message);
  }
});
