import { Request, Response } from 'express';
import { db } from '../config/database.ts';
import { ResponseHelper } from '../utils/response.ts';
import { logger } from '../utils/logger.ts';
import { asyncHandler } from './router.ts';

export const public = true; // This route doesn't require authentication

export const GET = asyncHandler(async (req: Request, res: Response) => {
  const healthCheck = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    version: process.env.npm_package_version || '1.0.0',
    environment: process.env.NODE_ENV || 'development',
    services: {
      database: 'unknown',
      redis: 'not_implemented', // For future use
    },
  };

  try {
    // Check database connection
    const dbResult = await db.query('SELECT NOW() as current_time');
    healthCheck.services.database = 'healthy';
    
    logger.debug('Health check performed', { status: 'healthy' });
    
    return ResponseHelper.success(res, healthCheck, 'Service is healthy');
  } catch (error) {
    logger.error('Health check failed:', error);
    
    healthCheck.status = 'unhealthy';
    healthCheck.services.database = 'unhealthy';
    
    return ResponseHelper.error(res, 'Service is unhealthy', undefined, 503);
  }
});
