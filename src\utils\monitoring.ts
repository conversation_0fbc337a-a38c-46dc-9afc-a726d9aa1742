import { Request, Response, NextFunction } from 'express';
import { logger } from './logger.ts';

interface PerformanceMetrics {
  requestCount: number;
  responseTime: {
    total: number;
    count: number;
    average: number;
    min: number;
    max: number;
  };
  statusCodes: Record<string, number>;
  endpoints: Record<string, {
    count: number;
    averageTime: number;
    totalTime: number;
  }>;
  errors: {
    count: number;
    types: Record<string, number>;
  };
}

class MonitoringService {
  private metrics: PerformanceMetrics = {
    requestCount: 0,
    responseTime: {
      total: 0,
      count: 0,
      average: 0,
      min: Infinity,
      max: 0,
    },
    statusCodes: {},
    endpoints: {},
    errors: {
      count: 0,
      types: {},
    },
  };

  private startTime: number = Date.now();

  recordRequest(req: Request, res: Response, responseTime: number): void {
    this.metrics.requestCount++;
    
    // Update response time metrics
    this.metrics.responseTime.total += responseTime;
    this.metrics.responseTime.count++;
    this.metrics.responseTime.average = this.metrics.responseTime.total / this.metrics.responseTime.count;
    this.metrics.responseTime.min = Math.min(this.metrics.responseTime.min, responseTime);
    this.metrics.responseTime.max = Math.max(this.metrics.responseTime.max, responseTime);

    // Update status code metrics
    const statusCode = res.statusCode.toString();
    this.metrics.statusCodes[statusCode] = (this.metrics.statusCodes[statusCode] || 0) + 1;

    // Update endpoint metrics
    const endpoint = `${req.method} ${req.route?.path || req.path}`;
    if (!this.metrics.endpoints[endpoint]) {
      this.metrics.endpoints[endpoint] = {
        count: 0,
        averageTime: 0,
        totalTime: 0,
      };
    }
    
    const endpointMetric = this.metrics.endpoints[endpoint];
    endpointMetric.count++;
    endpointMetric.totalTime += responseTime;
    endpointMetric.averageTime = endpointMetric.totalTime / endpointMetric.count;

    // Log slow requests
    if (responseTime > 1000) {
      logger.warn('Slow request detected', {
        method: req.method,
        path: req.path,
        responseTime: `${responseTime}ms`,
        statusCode: res.statusCode,
        userAgent: req.get('User-Agent'),
        ip: req.ip,
      });
    }
  }

  recordError(error: Error, req?: Request): void {
    this.metrics.errors.count++;
    
    const errorType = error.constructor.name;
    this.metrics.errors.types[errorType] = (this.metrics.errors.types[errorType] || 0) + 1;

    logger.error('Application error recorded', {
      error: error.message,
      type: errorType,
      stack: error.stack,
      path: req?.path,
      method: req?.method,
      ip: req?.ip,
    });
  }

  getMetrics(): PerformanceMetrics & { uptime: number } {
    return {
      ...this.metrics,
      uptime: Date.now() - this.startTime,
    };
  }

  getHealthStatus(): {
    status: 'healthy' | 'degraded' | 'unhealthy';
    checks: Record<string, boolean>;
    metrics: any;
  } {
    const checks = {
      responseTime: this.metrics.responseTime.average < 1000,
      errorRate: this.getErrorRate() < 0.05, // Less than 5% error rate
      memoryUsage: this.getMemoryUsagePercent() < 0.9, // Less than 90% memory usage
    };

    const allHealthy = Object.values(checks).every(check => check);
    const someUnhealthy = Object.values(checks).some(check => !check);

    let status: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';
    if (someUnhealthy && !allHealthy) {
      status = 'degraded';
    } else if (!allHealthy) {
      status = 'unhealthy';
    }

    return {
      status,
      checks,
      metrics: {
        averageResponseTime: this.metrics.responseTime.average,
        errorRate: this.getErrorRate(),
        memoryUsage: this.getMemoryUsagePercent(),
        requestCount: this.metrics.requestCount,
      },
    };
  }

  private getErrorRate(): number {
    if (this.metrics.requestCount === 0) return 0;
    return this.metrics.errors.count / this.metrics.requestCount;
  }

  private getMemoryUsagePercent(): number {
    const usage = process.memoryUsage();
    // Approximate available memory (this is a simplified calculation)
    const totalMemory = usage.heapTotal + usage.external + usage.arrayBuffers;
    const usedMemory = usage.heapUsed;
    return usedMemory / totalMemory;
  }

  reset(): void {
    this.metrics = {
      requestCount: 0,
      responseTime: {
        total: 0,
        count: 0,
        average: 0,
        min: Infinity,
        max: 0,
      },
      statusCodes: {},
      endpoints: {},
      errors: {
        count: 0,
        types: {},
      },
    };
    this.startTime = Date.now();
  }
}

export const monitoring = new MonitoringService();

// Middleware to track request metrics
export const metricsMiddleware = (req: Request, res: Response, next: NextFunction) => {
  const startTime = Date.now();

  res.on('finish', () => {
    const responseTime = Date.now() - startTime;
    monitoring.recordRequest(req, res, responseTime);
  });

  next();
};

// Error tracking middleware
export const errorTrackingMiddleware = (error: Error, req: Request, res: Response, next: NextFunction) => {
  monitoring.recordError(error, req);
  next(error);
};

// Periodic metrics logging
export const startMetricsLogging = (intervalMs: number = 300000) => { // 5 minutes default
  setInterval(() => {
    const metrics = monitoring.getMetrics();
    const health = monitoring.getHealthStatus();
    
    logger.info('Periodic metrics report', {
      metrics: {
        requestCount: metrics.requestCount,
        averageResponseTime: metrics.responseTime.average,
        errorCount: metrics.errors.count,
        errorRate: (metrics.errors.count / metrics.requestCount) || 0,
        uptime: metrics.uptime,
      },
      health: health.status,
      topEndpoints: Object.entries(metrics.endpoints)
        .sort(([,a], [,b]) => b.count - a.count)
        .slice(0, 5)
        .map(([endpoint, data]) => ({ endpoint, ...data })),
    });
  }, intervalMs);
};

export default monitoring;
