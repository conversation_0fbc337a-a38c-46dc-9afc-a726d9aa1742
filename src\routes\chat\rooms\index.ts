import { Request, Response } from 'express';
import { ChatService } from '../../../services/chat.service.ts';
import { ResponseHelper } from '../../../utils/response.ts';
import { validateSchema, validateQuery, schemas } from '../../../utils/validation.ts';
import { paginationMiddleware } from '../../../middleware/common.middleware.ts';
import { requirePermission } from '../../../middleware/auth.middleware.ts';
import { logger } from '../../../utils/logger.ts';
import { asyncHandler } from '../../router.ts';
import { AuthenticatedRequest } from '../../../middleware/auth.middleware.ts';

// GET /chat/rooms - Get user's chat rooms
export const GET = asyncHandler(async (req: Request, res: Response) => {
  try {
    const user = (req as AuthenticatedRequest).user;
    const pagination = req.pagination!;

    const result = await ChatService.getUserRooms(user.id, pagination);

    return ResponseHelper.paginated(
      res,
      result.rooms,
      pagination.page,
      pagination.limit,
      result.total,
      'Chat rooms retrieved successfully'
    );

  } catch (error) {
    logger.error('Error fetching chat rooms:', error);
    
    const message = error instanceof Error ? error.message : 'Failed to fetch chat rooms';
    return ResponseHelper.error(res, message);
  }
});

// POST /chat/rooms - Create new chat room
export const POST = asyncHandler(async (req: Request, res: Response) => {
  try {
    const user = (req as AuthenticatedRequest).user;
    const { name, type, participant_ids } = req.body;

    // For direct chats, ensure only 2 participants (including creator)
    if (type === 'direct') {
      if (participant_ids.length !== 1) {
        return ResponseHelper.error(res, 'Direct chats must have exactly one other participant');
      }

      // Check if direct room already exists
      const existingRoom = await ChatService.getDirectRoom(user.id, participant_ids[0]);
      if (existingRoom) {
        return ResponseHelper.success(res, existingRoom, 'Direct chat already exists');
      }
    }

    const room = await ChatService.createRoom(user.id, type, participant_ids, name);

    logger.info('Chat room created', { room_id: room.id, created_by: user.id, type });

    return ResponseHelper.created(res, room, 'Chat room created successfully');

  } catch (error) {
    logger.error('Error creating chat room:', error);
    
    const message = error instanceof Error ? error.message : 'Failed to create chat room';
    return ResponseHelper.error(res, message);
  }
});

export const middleware = [
  requirePermission('chat.create'),
  paginationMiddleware,
];

// Override middleware for specific methods
GET.middleware = [
  requirePermission('chat.read'),
  paginationMiddleware,
];

POST.middleware = [
  requirePermission('chat.create'),
  validateSchema(schemas.chat.createRoom),
];
