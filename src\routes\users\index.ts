import { Request, Response } from 'express';
import { UserService } from '../../services/user.service.ts';
import { ResponseHelper } from '../../utils/response.ts';
import { validateQuery, schemas } from '../../utils/validation.ts';
import { paginationMiddleware } from '../../middleware/common.middleware.ts';
import { requirePermission } from '../../middleware/auth.middleware.ts';
import { logger } from '../../utils/logger.ts';
import { asyncHandler } from '../router.ts';
import Joi from 'joi';

const getUsersQuerySchema = schemas.pagination.keys({
  user_type: Joi.string().valid('worker', 'company').optional(),
  is_active: Joi.boolean().optional(),
  search: Joi.string().min(1).max(100).optional(),
});

export const middleware = [
  requirePermission('admin.users'),
  paginationMiddleware,
  validateQuery(getUsersQuerySchema),
];

export const GET = asyncHandler(async (req: Request, res: Response) => {
  try {
    const pagination = req.pagination!;
    const filters = {
      user_type: req.query.user_type as 'worker' | 'company' | undefined,
      is_active: req.query.is_active as boolean | undefined,
      search: req.query.search as string | undefined,
    };

    const result = await UserService.getUsers(pagination, filters);

    return ResponseHelper.paginated(
      res,
      result.users,
      pagination.page,
      pagination.limit,
      result.total,
      'Users retrieved successfully'
    );

  } catch (error) {
    logger.error('Error fetching users:', error);
    
    const message = error instanceof Error ? error.message : 'Failed to fetch users';
    return ResponseHelper.error(res, message);
  }
});
